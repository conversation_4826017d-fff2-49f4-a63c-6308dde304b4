#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天眼查企业信息爬虫 - 集成版本
针对IDEALAB项目优化，支持进度回调和异步操作
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
import re
from datetime import datetime
from urllib.parse import urljoin
from enum import Enum, auto
import asyncio
from typing import Optional, Dict, Any, Callable
import logging

# Selenium相关导入（可选）
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

from .progress_logger import ProgressLogger

class CompanyType(Enum):
    """枚举公司类型"""
    REGULAR = auto()      # 普通公司
    LISTED = auto()       # 上市公司
    PARTNERSHIP = auto()  # 有限合伙/普通合伙

class TianyanchaScraperAsync:
    """天眼查企业信息爬虫 - 异步版本"""
    
    def __init__(self, use_selenium=True, headless=True, progress_logger: Optional[ProgressLogger] = None):
        """
        初始化爬虫
        Args:
            use_selenium: 是否使用Selenium模式，默认True
            headless: 是否在无头模式下运行Selenium，默认True
            progress_logger: 进度日志记录器
        """
        # 强制使用Selenium模式，不再支持基础模式
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium is required for this crawler. Please install: pip install selenium webdriver-manager")

        self.use_selenium = True  # 强制使用Selenium
        self.headless = headless
        self.driver = None
        self.progress_logger = progress_logger
        self.logger = logging.getLogger("tianyancha_scraper")

        if self.progress_logger:
            self.progress_logger._send_user_log("info", "🚀 启用Selenium模式（完整股东信息）")
            self.progress_logger._send_user_log("info", "💡 提示：确保已启动调试模式Chrome并登录天眼查")
    
    async def setup_selenium(self):
        """异步设置Selenium环境"""
        try:
            if self.progress_logger:
                self.progress_logger._send_user_log("info", "正在初始化浏览器...")

            # 在线程中执行Selenium设置，避免阻塞
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, self._setup_selenium_sync)

            if success and self.progress_logger:
                self.progress_logger._send_user_log("success", "✅ 浏览器初始化成功")
            elif self.progress_logger:
                self.progress_logger._send_user_log("error", "❌ 浏览器初始化失败")

            if not success:
                raise RuntimeError("Selenium initialization failed")

            return success

        except Exception as e:
            self.logger.error(f"Selenium setup failed: {e}")
            if self.progress_logger:
                self.progress_logger._send_user_log("error", f"❌ 浏览器初始化失败: {str(e)}")
            raise
    
    def _setup_selenium_sync(self):
        """同步版本的Selenium设置"""
        try:
            # 首先尝试连接到现有浏览器（推荐方式，绕过反爬虫）
            try:
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.implicitly_wait(10)

                # 测试连接是否成功
                self.driver.current_url

                if self.progress_logger:
                    self.progress_logger._send_user_log("success", "✅ 成功连接到现有浏览器会话")

                return True

            except Exception as e:
                self.logger.info(f"无法连接到现有浏览器: {e}")
                if self.progress_logger:
                    self.progress_logger._send_user_log("warning", "⚠️ 无法连接到现有浏览器，启动新浏览器实例")

            # 如果连接现有浏览器失败，启动新实例
            chrome_options = Options()

            # 如果设置为headless模式
            if self.headless:
                chrome_options.add_argument('--headless')

            # 反爬虫伪装
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.implicitly_wait(10)

            if self.progress_logger:
                self.progress_logger._send_user_log("success", "✅ 新浏览器实例启动成功")
                self.progress_logger._send_user_log("info", "💡 提示：手动打开Chrome浏览器并访问天眼查登录，然后重启服务以获得更好效果")

            return True

        except Exception as e:
            self.logger.error(f"Chrome driver setup failed: {e}")
            if self.progress_logger:
                self.progress_logger._send_user_log("error", f"❌ Selenium设置失败: {str(e)}")
            return False
    
    async def search_company(self, company_name: str) -> Optional[Dict[str, str]]:
        """
        异步搜索公司信息
        """
        if self.progress_logger:
            self.progress_logger._send_user_log("info", f"正在搜索公司: {company_name}")

        # 确保Selenium已初始化（如果需要）
        if self.use_selenium and not self.driver:
            await self.setup_selenium()

        search_url = f"https://www.tianyancha.com/nsearch?key={company_name}"

        try:
            # 在线程池中执行网络请求
            loop = asyncio.get_event_loop()
            html_content = await loop.run_in_executor(None, self._get_page_sync, search_url)
            
            if not html_content:
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找第一个搜索结果
            first_result_link = soup.select_one('div.index_search-box__7YVh6 a.index_alink__zcia5')
            
            if first_result_link:
                company_url = first_result_link.get('href')
                if company_url and not company_url.startswith('http'):
                    company_url = urljoin('https://www.tianyancha.com', company_url)
                
                company_title_element = first_result_link.find('span')
                company_title = company_title_element.get_text(strip=True) if company_title_element else "未知公司名称"
                
                if self.progress_logger:
                    self.progress_logger._send_user_log("success", f"✅ 找到公司: {company_title}")
                
                return {'name': company_title, 'url': company_url}
            else:
                if self.progress_logger:
                    self.progress_logger._send_user_log("warning", f"未找到公司: {company_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Search failed for {company_name}: {e}")
            if self.progress_logger:
                self.progress_logger.log_error(company_name, f"搜索失败: {str(e)}")
            return None
    
    async def scrape_company_info(self, company_url: str, direction: str = 'both') -> Optional[Dict[str, Any]]:
        """
        异步爬取公司详细信息
        """
        try:
            # 设置Selenium（如果需要）
            if self.use_selenium and not self.driver:
                await self.setup_selenium()
            
            # 在线程池中执行爬取
            loop = asyncio.get_event_loop()
            data = await loop.run_in_executor(
                None, 
                self._scrape_company_sync, 
                company_url, 
                direction
            )
            
            return data
            
        except Exception as e:
            self.logger.error(f"Scraping failed for {company_url}: {e}")
            if self.progress_logger:
                self.progress_logger.log_error("系统", f"爬取失败: {str(e)}")
            return None
    
    def _scrape_company_sync(self, company_url: str, direction: str) -> Dict[str, Any]:
        """同步版本的爬取函数"""
        data = {
            'url': company_url,
            'source_url': company_url,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'basic_info': {},
            'shareholders_info': [],
            'investments_info': []
        }
        
        try:
            # 获取页面内容
            html_content = self._get_page_sync(company_url)
            if not html_content:
                return data
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 解析基本信息
            data['basic_info'] = self._parse_basic_info(soup)
            
            # 解析标签
            tags_list = self._parse_company_tags(soup)
            if tags_list:
                data['basic_info']['标签'] = '|'.join(tags_list)
            
            # 根据方向解析关系信息
            if direction in ['up', 'both']:
                data['shareholders_info'] = self._parse_shareholders_info(soup)
            
            if direction in ['down', 'both'] and self.use_selenium and self.driver:
                data['investments_info'] = self._parse_investment_info()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Sync scraping failed: {e}")
            raise
    
    def _get_page_sync(self, url: str) -> Optional[str]:
        """同步获取页面内容 - 仅使用Selenium"""
        if not self.driver:
            raise RuntimeError("Selenium driver not initialized. Call setup_selenium() first.")
        return self._get_page_selenium(url)
    

    
    def _get_page_selenium(self, url: str) -> Optional[str]:
        """使用Selenium获取页面"""
        try:
            self.driver.get(url)
            
            # 等待页面加载
            if "/nsearch" in url:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.index_list-content__wjkNi"))
                )
            elif "/company/" in url:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "股东名称" in driver.page_source or "股东信息" in driver.page_source
                )
            
            time.sleep(random.uniform(1, 2))
            return self.driver.page_source
            
        except TimeoutException:
            if self.progress_logger:
                self.progress_logger.log_anti_spider_detected()
            return self.driver.page_source if self.driver else None
        except Exception as e:
            self.logger.error(f"Selenium page fetch failed: {e}")
            raise
    
    def _parse_basic_info(self, soup) -> Dict[str, str]:
        """解析企业基本信息"""
        basic_info = {}
        
        try:
            # 解析企业名称
            h1_tag = soup.find('h1', class_=re.compile(r'company-name|index_name'))
            if h1_tag:
                name_span = h1_tag.find('span', class_=re.compile(r'index_name'))
                if name_span:
                    basic_info['企业名称'] = name_span.get_text(strip=True)
                
                # 解析状态
                status_tag = h1_tag.find('div', class_='index_reg-status-tag__ES7dF')
                if status_tag:
                    basic_info['经营状态'] = status_tag.get_text(strip=True)
            
            # 解析信息表格
            info_table = soup.find('table', class_='index_tableBox__ZadJW')
            if info_table:
                rows = info_table.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        self._parse_table_cell(cells, basic_info)
            
            # 解析法定代表人
            legal_rep_td = soup.find('td', text=re.compile(r'^\s*法定代表人\s*$'))
            if legal_rep_td and legal_rep_td.find_next_sibling('td'):
                rep_link = legal_rep_td.find_next_sibling('td').find('a', class_='link-click')
                if rep_link:
                    basic_info['法定代表人'] = rep_link.get_text(strip=True)
            
        except Exception as e:
            self.logger.error(f"Basic info parsing error: {e}")
        
        return basic_info
    
    def _parse_table_cell(self, cells, info_dict):
        """解析表格单元格"""
        for i in range(0, len(cells) - 1, 2):
            if i + 1 < len(cells):
                title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', cells[i].get_text(strip=True))
                if title:
                    value = cells[i + 1].get_text(strip=True)
                    info_dict[title] = value
    
    def _parse_shareholders_info(self, soup) -> list:
        """解析股东信息"""
        shareholders_info = []
        
        try:
            shareholder_table = self._find_shareholder_table(soup)
            if not shareholder_table:
                return shareholders_info
            
            thead = shareholder_table.find('thead')
            header_cells = []
            if thead:
                header_cells = [th.get_text(strip=True) for th in thead.find_all('th')]
            
            tbody = shareholder_table.find('tbody')
            if not tbody:
                return shareholders_info
            
            rows = tbody.find_all('tr')
            
            for row in rows:
                cells = row.find_all('td')
                if not cells:
                    continue
                
                shareholder_data = self._parse_shareholder_row(cells, header_cells)
                if shareholder_data.get('股东名称'):
                    shareholders_info.append(shareholder_data)
            
        except Exception as e:
            self.logger.error(f"Shareholders parsing error: {e}")
        
        return shareholders_info
    
    def _parse_shareholder_row(self, cells, headers) -> Dict[str, str]:
        """解析股东信息行"""
        data = {}
        
        for idx, cell in enumerate(cells):
            header = headers[idx] if idx < len(headers) else ''
            header_clean = re.sub(r'\s+', '', header)
            
            # 股东名称和链接
            if any(key in header_clean for key in ['股东名称', '合伙人名称', '股东/发起人']):
                link_tag = cell.find('a', class_='link-click')
                if link_tag:
                    data['股东名称'] = link_tag.get_text(strip=True)
                    data['股东链接'] = link_tag.get('href', '')
                else:
                    data['股东名称'] = cell.get_text(strip=True)
            
            # 持股比例
            elif '持股比例' in header_clean or '出资比例' in header_clean:
                span_tag = cell.find('span')
                if span_tag:
                    data['持股比例'] = span_tag.get_text(strip=True)
                else:
                    data['持股比例'] = cell.get_text(strip=True)
            
            # 认缴出资额
            elif '认缴出资额' in header_clean:
                span_tag = cell.find('span')
                if span_tag:
                    data['认缴出资额万元'] = span_tag.get_text(strip=True)
                else:
                    data['认缴出资额万元'] = cell.get_text(strip=True)
        
        return data
    
    def _parse_investment_info(self) -> list:
        """解析对外投资信息（需要Selenium）"""
        if not self.driver:
            return []
        
        investment_info = []
        
        try:
            # 定位对外投资板块
            investment_section = self.driver.find_element(By.CSS_SELECTOR, 'div[data-dim="inverst"]')
            
            # 获取当前页面的表格行
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            current_investment_section_soup = soup.find('div', attrs={'data-dim': 'inverst'})
            
            if not current_investment_section_soup:
                return investment_info
            
            tbody = current_investment_section_soup.find('tbody')
            if not tbody:
                return investment_info
            
            rows = tbody.find_all('tr')
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) < 5:
                    continue
                
                try:
                    company_tag = cells[1].find('a', class_='link-click')
                    if company_tag:
                        investment_data = {
                            "被投公司名称": company_tag.text.strip(),
                            "被投公司链接": company_tag.get('href', ''),
                            "状态": cells[2].text.strip(),
                            "成立日期": cells[3].text.strip(),
                            "投资占比": cells[4].text.strip(),
                            "注册资本": cells[5].text.strip() if len(cells) > 5 else '',
                        }
                        investment_info.append(investment_data)
                except Exception as e:
                    self.logger.warning(f"Investment row parsing error: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Investment parsing error: {e}")
        
        return investment_info
    
    def _parse_company_tags(self, soup) -> list:
        """解析企业标签"""
        tags = []
        
        try:
            tag_container = soup.find('div', class_='index_tag-list-content__E8sLp')
            if tag_container:
                for tag_div in tag_container.find_all('div'):
                    classes = tag_div.get('class', [])
                    if any(cls.startswith('index_company-tag') for cls in classes):
                        text = tag_div.get_text(strip=True)
                        if text:
                            tags.append(text)
        except Exception as e:
            self.logger.error(f"Tags parsing error: {e}")
        
        return tags
    
    def _find_shareholder_table(self, soup):
        """查找股东信息表格"""
        for table in soup.find_all('table'):
            thead = table.find('thead')
            if not thead:
                continue
            header_text = thead.get_text()
            if ('股东' in header_text or '合伙人' in header_text) and (
                '持股' in header_text or '出资比例' in header_text):
                return table
        return None
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            self.logger.info("Browser closed")