import React, { useState, useEffect, useRef } from 'react';
import {
    Steps,
    Progress,
    Typography,
    List,
    Tag,
    Space,
    Spin,
    Card,
    Statistic,
    Row,
    Col,
    Empty
} from 'antd';
import {
    CheckCircleOutlined,
    SyncOutlined,
    TrophyOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Step } = Steps;

export interface LogEntry {
    timestamp: string;
    level: 'info' | 'success' | 'warning' | 'error' | 'progress';
    message: string;
    phase: string;
    progress?: number;
}

export interface TaskStatus {
    id: string;
    name: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    total_steps: number;
    processed_items: number;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    error_message?: string;
    duration?: number;
    estimated_duration?: number;
}

export interface ProgressStep {
    title: string;
    description?: string;
}

export interface ProgressManagerProps {
    taskStatus: TaskStatus | null;
    logs: LogEntry[];
    steps: ProgressStep[];
    currentStep: number;
    startTime: Date | null;
    estimatedDuration?: number;
    showStatistics?: boolean;
    compact?: boolean;
    maxLogs?: number;
    className?: string;
    style?: React.CSSProperties;
}

export const ProgressManager: React.FC<ProgressManagerProps> = ({
    taskStatus,
    logs,
    steps,
    currentStep,
    startTime,
    estimatedDuration = 0,
    showStatistics = true,
    compact = false,
    maxLogs = 100,
    className,
    style
}) => {
    const logsEndRef = useRef<HTMLDivElement>(null);

    // 自动滚动到日志底部
    const scrollToBottom = () => {
        logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [logs]);

    // 计算已用时间
    const getElapsedTime = () => {
        if (!startTime) return 0;
        return Math.floor((Date.now() - startTime.getTime()) / 1000);
    };

    // 获取日志颜色
    const getLogColor = (level: LogEntry['level']) => {
        const colors = {
            'info': 'default',
            'success': 'success',
            'warning': 'warning',
            'error': 'error',
            'progress': 'processing'
        };
        return colors[level] || 'default';
    };

    // 获取步骤状态
    const getStepStatus = (stepIndex: number) => {
        if (!taskStatus) return 'wait';

        if (taskStatus.status === 'failed' && currentStep >= stepIndex) return 'error';
        if (currentStep > stepIndex) return 'finish';
        if (currentStep === stepIndex) {
            return taskStatus.status === 'running' ? 'process' : 'wait';
        }
        return 'wait';
    };

    // 获取步骤图标
    const getStepIcon = (stepIndex: number) => {
        const status = getStepStatus(stepIndex);
        if (status === 'process') return <SyncOutlined spin />;
        if (status === 'finish' && stepIndex === steps.length - 1 && taskStatus?.status === 'completed') {
            return <TrophyOutlined />;
        }
        return undefined;
    };

    // 获取状态显示文本
    const getStatusText = () => {
        if (!taskStatus) return '未知';
        switch (taskStatus.status) {
            case 'running': return '运行中';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            case 'pending': return '等待中';
            case 'cancelled': return '已取消';
            default: return '未知';
        }
    };

    // 获取状态颜色
    const getStatusColor = () => {
        if (!taskStatus) return '#1890ff';
        switch (taskStatus.status) {
            case 'completed': return '#3f8600';
            case 'failed': return '#cf1322';
            case 'cancelled': return '#d46b08';
            default: return '#1890ff';
        }
    };

    return (
        <div className={className} style={style}>
            {/* 统计信息 */}
            {showStatistics && taskStatus && (
                <Card
                    size="small"
                    style={{
                        marginBottom: 16,
                        background: 'rgba(255, 255, 255, 0.08)',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        borderRadius: '12px'
                    }}
                >
                    <Row gutter={16}>
                        <Col span={compact ? 12 : 6}>
                            <Statistic
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>任务名称</span>}
                                value={taskStatus.name}
                                valueStyle={{
                                    fontSize: compact ? '14px' : '16px',
                                    color: 'rgba(255, 255, 255, 0.95)'
                                }}
                            />
                        </Col>
                        <Col span={compact ? 12 : 6}>
                            <Statistic
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>已用时间</span>}
                                value={getElapsedTime()}
                                suffix="秒"
                                prefix={<ClockCircleOutlined style={{ color: '#2563eb' }} />}
                                valueStyle={{
                                    fontSize: compact ? '14px' : '16px',
                                    color: 'rgba(255, 255, 255, 0.95)'
                                }}
                            />
                        </Col>
                        {!compact && (
                            <>
                                <Col span={6}>
                                    <Statistic
                                        title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>预计时间</span>}
                                        value={estimatedDuration}
                                        suffix="秒"
                                        valueStyle={{
                                            fontSize: '16px',
                                            color: 'rgba(255, 255, 255, 0.95)'
                                        }}
                                    />
                                </Col>
                                <Col span={6}>
                                    <Statistic
                                        title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>当前状态</span>}
                                        value={getStatusText()}
                                        valueStyle={{
                                            color: getStatusColor(),
                                            fontSize: '16px'
                                        }}
                                    />
                                </Col>
                            </>
                        )}
                    </Row>
                </Card>
            )}

            {/* 进度步骤 */}
            {steps.length > 0 && (
                <div style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    padding: '16px',
                    marginBottom: 24
                }}>
                    <Steps
                        current={currentStep}
                        size={compact ? "small" : "default"}
                        direction={compact ? "vertical" : "horizontal"}
                        style={{
                            '--ant-steps-icon-size': '24px',
                            '--ant-steps-title-color': 'rgba(255, 255, 255, 0.95)',
                            '--ant-steps-description-color': 'rgba(255, 255, 255, 0.75)',
                            '--ant-steps-wait-icon-color': 'rgba(255, 255, 255, 0.35)',
                            '--ant-steps-finish-icon-color': '#10b981',
                            '--ant-steps-process-icon-color': '#2563eb'
                        } as React.CSSProperties}
                    >
                        {steps.map((step, index) => (
                            <Step
                                key={index}
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.95)' }}>{step.title}</span>}
                                description={!compact ? <span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>{step.description}</span> : undefined}
                                icon={getStepIcon(index)}
                                status={getStepStatus(index)}
                            />
                        ))}
                    </Steps>
                </div>
            )}

            {/* 进度条 */}
            <div style={{
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: 16
            }}>
                <div style={{
                    marginBottom: '8px',
                    color: 'rgba(255, 255, 255, 0.75)',
                    fontSize: '14px',
                    fontWeight: '500'
                }}>
                    整体进度
                </div>
                <Progress
                    percent={taskStatus?.progress || 0}
                    status={
                        taskStatus?.status === 'failed' ? 'exception' :
                        taskStatus?.status === 'completed' ? 'success' :
                        'active'
                    }
                    showInfo={true}
                    strokeColor={{
                        '0%': '#2563eb',
                        '100%': '#0891b2'
                    }}
                    trailColor="rgba(255, 255, 255, 0.1)"
                    style={{
                        '--ant-progress-text-color': 'rgba(255, 255, 255, 0.95)'
                    } as React.CSSProperties}
                />
            </div>

            {/* 详细统计 */}
            {!compact && taskStatus && (
                <Row gutter={16} style={{ marginBottom: 16 }}>
                    <Col span={8}>
                        <Card
                            size="small"
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: '8px'
                            }}
                        >
                            <Statistic
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>已处理项目</span>}
                                value={taskStatus.processed_items}
                                prefix={<CheckCircleOutlined style={{ color: '#10b981' }} />}
                                valueStyle={{ color: 'rgba(255, 255, 255, 0.95)' }}
                            />
                        </Card>
                    </Col>
                    <Col span={8}>
                        <Card
                            size="small"
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: '8px'
                            }}
                        >
                            <Statistic
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>总步骤</span>}
                                value={taskStatus.total_steps || 0}
                                valueStyle={{ color: 'rgba(255, 255, 255, 0.95)' }}
                            />
                        </Card>
                    </Col>
                    <Col span={8}>
                        <Card
                            size="small"
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: '8px'
                            }}
                        >
                            <Statistic
                                title={<span style={{ color: 'rgba(255, 255, 255, 0.75)' }}>完成度</span>}
                                value={taskStatus.progress}
                                suffix="%"
                                prefix={
                                    taskStatus.status === 'running' ? <SyncOutlined spin style={{ color: '#2563eb' }} /> :
                                    taskStatus.status === 'completed' ? <CheckCircleOutlined style={{ color: '#10b981' }} /> :
                                    taskStatus.status === 'failed' ? <ExclamationCircleOutlined style={{ color: '#dc2626' }} /> :
                                    <ClockCircleOutlined style={{ color: '#f97316' }} />
                                }
                                valueStyle={{ color: 'rgba(255, 255, 255, 0.95)' }}
                            />
                        </Card>
                    </Col>
                </Row>
            )}

            {/* 实时日志 */}
            <Card
                title={
                    <Space>
                        <SyncOutlined spin={taskStatus?.status === 'running'} />
                        <span style={{ color: 'rgba(255, 255, 255, 0.95)' }}>实时日志</span>
                    </Space>
                }
                size="small"
                style={{
                    background: 'rgba(255, 255, 255, 0.08)',
                    border: '1px solid rgba(255, 255, 255, 0.15)',
                    borderRadius: '12px'
                }}
            >
                <div style={{
                    height: compact ? '150px' : '200px',
                    overflowY: 'auto',
                    backgroundColor: 'rgba(15, 23, 42, 0.6)',
                    padding: '12px',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                }}>
                    {logs.length === 0 ? (
                        <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                                <span style={{ color: 'rgba(255, 255, 255, 0.55)' }}>
                                    暂无日志信息
                                </span>
                            }
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%'
                            }}
                        />
                    ) : (
                        <List
                            size="small"
                            dataSource={logs.slice(-maxLogs)}
                            rowKey={(log) => `${log.timestamp}-${log.phase}-${(log.message||'').slice(0,24)}`}
                            renderItem={(log) => (
                                <List.Item style={{
                                    padding: '6px 0',
                                    borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
                                    margin: 0
                                }}>
                                    <Space size="small" style={{ width: '100%' }}>
                                        <Text style={{
                                            fontSize: '10px',
                                            minWidth: '60px',
                                            color: 'rgba(255, 255, 255, 0.55)',
                                            fontFamily: 'JetBrains Mono, monospace'
                                        }}>
                                            {new Date(log.timestamp).toLocaleTimeString()}
                                        </Text>
                                        <Tag
                                            color={getLogColor(log.level)}
                                            style={{
                                                fontSize: '9px',
                                                fontWeight: '500',
                                                borderRadius: '4px',
                                                margin: 0
                                            }}
                                        >
                                            {log.level.toUpperCase()}
                                        </Tag>
                                        <Text style={{
                                            fontSize: '12px',
                                            flex: 1,
                                            color: 'rgba(255, 255, 255, 0.85)',
                                            lineHeight: '1.4'
                                        }}>
                                            {log.message}
                                        </Text>
                                    </Space>
                                </List.Item>
                            )}
                        />
                    )}
                    <div ref={logsEndRef} />
                </div>
            </Card>

            {/* 错误信息 */}
            {taskStatus?.error_message && (
                <Alert
                    message={<span style={{ color: 'rgba(255, 255, 255, 0.95)' }}>任务执行失败</span>}
                    description={<span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{taskStatus.error_message}</span>}
                    type="error"
                    showIcon
                    style={{
                        marginTop: 16,
                        backgroundColor: 'rgba(220, 38, 38, 0.1)',
                        border: '1px solid rgba(220, 38, 38, 0.3)',
                        borderRadius: '8px'
                    }}
                />
            )}

            {/* 完成提示 */}
            {taskStatus?.status === 'completed' && (
                <Alert
                    message={<span style={{ color: 'rgba(255, 255, 255, 0.95)' }}>任务执行完成！</span>}
                    description={
                        <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>
                            任务已成功完成，共耗时 {taskStatus.duration || getElapsedTime()} 秒。
                        </span>
                    }
                    type="success"
                    showIcon
                    style={{
                        marginTop: 16,
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        border: '1px solid rgba(16, 185, 129, 0.3)',
                        borderRadius: '8px'
                    }}
                />
            )}
        </div>
    );
};

export default ProgressManager;
