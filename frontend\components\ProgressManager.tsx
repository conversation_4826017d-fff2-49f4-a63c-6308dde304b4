import React, { useState, useEffect, useRef } from 'react';
import {
    Steps,
    Progress,
    Typography,
    List,
    Tag,
    Space,
    Spin,
    Card,
    Statistic,
    Row,
    Col,
    Empty
} from 'antd';
import {
    CheckCircleOutlined,
    SyncOutlined,
    TrophyOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Step } = Steps;

export interface LogEntry {
    timestamp: string;
    level: 'info' | 'success' | 'warning' | 'error' | 'progress';
    message: string;
    phase: string;
    progress?: number;
}

export interface TaskStatus {
    id: string;
    name: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    total_steps: number;
    processed_items: number;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    error_message?: string;
    duration?: number;
    estimated_duration?: number;
}

export interface ProgressStep {
    title: string;
    description?: string;
}

export interface ProgressManagerProps {
    taskStatus: TaskStatus | null;
    logs: LogEntry[];
    steps: ProgressStep[];
    currentStep: number;
    startTime: Date | null;
    estimatedDuration?: number;
    showStatistics?: boolean;
    compact?: boolean;
    maxLogs?: number;
    className?: string;
    style?: React.CSSProperties;
}

export const ProgressManager: React.FC<ProgressManagerProps> = ({
    taskStatus,
    logs,
    steps,
    currentStep,
    startTime,
    estimatedDuration = 0,
    showStatistics = true,
    compact = false,
    maxLogs = 100,
    className,
    style
}) => {
    const logsEndRef = useRef<HTMLDivElement>(null);

    // 自动滚动到日志底部
    const scrollToBottom = () => {
        logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [logs]);

    // 计算已用时间
    const getElapsedTime = () => {
        if (!startTime) return 0;
        return Math.floor((Date.now() - startTime.getTime()) / 1000);
    };

    // 获取日志颜色
    const getLogColor = (level: LogEntry['level']) => {
        const colors = {
            'info': 'default',
            'success': 'success',
            'warning': 'warning',
            'error': 'error',
            'progress': 'processing'
        };
        return colors[level] || 'default';
    };

    // 获取步骤状态
    const getStepStatus = (stepIndex: number) => {
        if (!taskStatus) return 'wait';

        if (taskStatus.status === 'failed' && currentStep >= stepIndex) return 'error';
        if (currentStep > stepIndex) return 'finish';
        if (currentStep === stepIndex) {
            return taskStatus.status === 'running' ? 'process' : 'wait';
        }
        return 'wait';
    };

    // 获取步骤图标
    const getStepIcon = (stepIndex: number) => {
        const status = getStepStatus(stepIndex);
        if (status === 'process') return <SyncOutlined spin />;
        if (status === 'finish' && stepIndex === steps.length - 1 && taskStatus?.status === 'completed') {
            return <TrophyOutlined />;
        }
        return undefined;
    };

    // 获取状态显示文本
    const getStatusText = () => {
        if (!taskStatus) return '未知';
        switch (taskStatus.status) {
            case 'running': return '运行中';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            case 'pending': return '等待中';
            case 'cancelled': return '已取消';
            default: return '未知';
        }
    };

    // 获取状态颜色
    const getStatusColor = () => {
        if (!taskStatus) return '#1890ff';
        switch (taskStatus.status) {
            case 'completed': return '#3f8600';
            case 'failed': return '#cf1322';
            case 'cancelled': return '#d46b08';
            default: return '#1890ff';
        }
    };

    return (
        <div className={className} style={style}>
            {/* 统计信息 */}
            {showStatistics && taskStatus && (
                <Card size="small" style={{ marginBottom: 16 }}>
                    <Row gutter={16}>
                        <Col span={compact ? 12 : 6}>
                            <Statistic 
                                title="任务名称" 
                                value={taskStatus.name}
                                valueStyle={{ fontSize: compact ? '14px' : '16px' }}
                            />
                        </Col>
                        <Col span={compact ? 12 : 6}>
                            <Statistic
                                title="已用时间"
                                value={getElapsedTime()}
                                suffix="秒"
                                prefix={<ClockCircleOutlined />}
                                valueStyle={{ fontSize: compact ? '14px' : '16px' }}
                            />
                        </Col>
                        {!compact && (
                            <>
                                <Col span={6}>
                                    <Statistic
                                        title="预计时间"
                                        value={estimatedDuration}
                                        suffix="秒"
                                        valueStyle={{ fontSize: '16px' }}
                                    />
                                </Col>
                                <Col span={6}>
                                    <Statistic
                                        title="当前状态"
                                        value={getStatusText()}
                                        valueStyle={{ 
                                            color: getStatusColor(),
                                            fontSize: '16px'
                                        }}
                                    />
                                </Col>
                            </>
                        )}
                    </Row>
                </Card>
            )}

            {/* 进度步骤 */}
            {steps.length > 0 && (
                <Steps
                    current={currentStep}
                    size={compact ? "small" : "default"}
                    style={{ marginBottom: 24 }}
                    direction={compact ? "vertical" : "horizontal"}
                >
                    {steps.map((step, index) => (
                        <Step
                            key={index}
                            title={step.title}
                            description={!compact ? step.description : undefined}
                            icon={getStepIcon(index)}
                            status={getStepStatus(index)}
                        />
                    ))}
                </Steps>
            )}

            {/* 进度条 */}
            <Progress
                percent={taskStatus?.progress || 0}
                status={
                    taskStatus?.status === 'failed' ? 'exception' :
                    taskStatus?.status === 'completed' ? 'success' :
                    'active'
                }
                showInfo={true}
                style={{ marginBottom: 16 }}
            />

            {/* 详细统计 */}
            {!compact && taskStatus && (
                <Row gutter={16} style={{ marginBottom: 16 }}>
                    <Col span={8}>
                        <Card size="small">
                            <Statistic
                                title="已处理项目"
                                value={taskStatus.processed_items}
                                prefix={<CheckCircleOutlined />}
                            />
                        </Card>
                    </Col>
                    <Col span={8}>
                        <Card size="small">
                            <Statistic
                                title="总步骤"
                                value={taskStatus.total_steps || 0}
                            />
                        </Card>
                    </Col>
                    <Col span={8}>
                        <Card size="small">
                            <Statistic
                                title="完成度"
                                value={taskStatus.progress}
                                suffix="%"
                                prefix={
                                    taskStatus.status === 'running' ? <SyncOutlined spin /> :
                                    taskStatus.status === 'completed' ? <CheckCircleOutlined /> :
                                    taskStatus.status === 'failed' ? <ExclamationCircleOutlined /> :
                                    <ClockCircleOutlined />
                                }
                            />
                        </Card>
                    </Col>
                </Row>
            )}

            {/* 实时日志 */}
            <Card
                title={
                    <Space>
                        <SyncOutlined spin={taskStatus?.status === 'running'} />
                        <span>实时日志</span>
                    </Space>
                }
                size="small"
            >
                <div style={{ 
                    height: compact ? '150px' : '200px', 
                    overflowY: 'auto', 
                    backgroundColor: '#fafafa', 
                    padding: '8px',
                    borderRadius: '4px'
                }}>
                    {logs.length === 0 ? (
                        <Empty 
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description="暂无日志信息"
                            style={{ 
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%',
                                color: '#999'
                            }}
                        />
                    ) : (
                        <List
                            size="small"
                            dataSource={logs.slice(-maxLogs)}
                            rowKey={(log) => `${log.timestamp}-${log.phase}-${(log.message||'').slice(0,24)}`}
                            renderItem={(log) => (
                                <List.Item style={{ padding: '4px 0', borderBottom: 'none' }}>
                                    <Space size="small" style={{ width: '100%' }}>
                                        <Text type="secondary" style={{ fontSize: '10px', minWidth: '60px' }}>
                                            {new Date(log.timestamp).toLocaleTimeString()}
                                        </Text>
                                        <Tag color={getLogColor(log.level)} style={{ fontSize: '10px' }}>
                                            {log.level.toUpperCase()}
                                        </Tag>
                                        <Text style={{ fontSize: '12px', flex: 1 }}>
                                            {log.message}
                                        </Text>
                                    </Space>
                                </List.Item>
                            )}
                        />
                    )}
                    <div ref={logsEndRef} />
                </div>
            </Card>

            {/* 错误信息 */}
            {taskStatus?.error_message && (
                <Alert
                    message="任务执行失败"
                    description={taskStatus.error_message}
                    type="error"
                    showIcon
                    style={{ marginTop: 16 }}
                />
            )}

            {/* 完成提示 */}
            {taskStatus?.status === 'completed' && (
                <Alert
                    message="任务执行完成！"
                    description={`任务已成功完成，共耗时 ${taskStatus.duration || getElapsedTime()} 秒。`}
                    type="success"
                    showIcon
                    style={{ marginTop: 16 }}
                />
            )}
        </div>
    );
};

export default ProgressManager;
