const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const winston = require('winston');
const http = require('http');
require('dotenv').config();

// const { authenticateToken, authorizePermission, optionalAuth, initializeRedis } = require('./middleware/auth');
const { authenticateToken, authorizePermission, optionalAuth } = require('./middleware/auth');
const {
  SERVICE_CONFIGS,
  PERMISSION_ROUTES,
  PUBLIC_ROUTES,
  createServiceLimiter,
  createServiceProxy,
  healthCheck
} = require('./config/services');

// WebSocket代理实例（全局单例）
const { createProxyMiddleware } = require('http-proxy-middleware');
const wsProxy = createProxyMiddleware({
  target: 'http://localhost:8003',
  changeOrigin: true,
  ws: true,
  pathRewrite: { '^/api/equity': '/equity' },
  logLevel: 'warn'
});

const app = express();
const PORT = process.env.PORT || 16576;

// 日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console()
  ]
});

// 中间件配置
app.use(helmet());
app.use(cors({
  origin: (origin, callback) => {
    // origin 是浏览器发来请求的源地址, e.g., 'http://30.c13.plus:62265'
    const allowedStaticOrigins = [
      'http://localhost:3000', // 本地开发环境
    ];

    // 从环境变量中添加允许的地址
    if (process.env.FRONTEND_URL) {
      allowedStaticOrigins.push(process.env.FRONTEND_URL);
    }

    // 从环境变量中添加额外的允许源（支持逗号分隔的多个地址）
    if (process.env.ALLOWED_ORIGINS) {
      const additionalOrigins = process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
      allowedStaticOrigins.push(...additionalOrigins);
    }

    // 如果是静态允许的地址之一, 则通过
    if (allowedStaticOrigins.includes(origin)) {
      return callback(null, true);
    }

    // 
    if (origin && origin.startsWith('http://**************/')) {
      return callback(null, true);
    }

    // 其他所有情况则拒绝
    const msg = `CORS policy does not allow access from the specified Origin: ${origin}.`;
    return callback(new Error(msg));
  },
  credentials: true
}));

// app.use(express.json({ limit: '10mb' }));

// // 初始化Redis连接
// initializeRedis().catch(err => {
//   logger.error('Failed to initialize Redis:', err);
// });

// 请求日志中间件
app.use((req, res, next) => {
  req.id = Date.now().toString(36) + Math.random().toString(36).substr(2);
  logger.info(`${req.method} ${req.url} - ${req.ip} - ID: ${req.id}`);
  next();
});

// 健康检查
app.get('/health', async (req, res) => {
  const serviceHealth = {};
  
  // 检查各服务健康状态
  for (const [serviceName, config] of Object.entries(SERVICE_CONFIGS)) {
    serviceHealth[serviceName] = await healthCheck(serviceName);
  }
  
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    gateway: {
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage()
    },
    services: serviceHealth
  });
});

// 路由权限检查中间件
const checkPermissions = (req, res, next) => {
  const path = req.path;

  // 已认证请求对象
  const proceedAuth = () => authenticateToken(req, res, () => next());

  // 检查是否为公开路由
  const isPublicRoute = PUBLIC_ROUTES.some(route => path.startsWith(route));
  if (isPublicRoute) {
    return next();
  }

  // 如果用户是 admin 角色，放行（避免权限映射遗漏导致阻塞）
  if (req.user && req.user.role === 'admin') {
    return next();
  }

  // 检查是否需要特定权限
  const requiredPermissions = PERMISSION_ROUTES[path];
  if (requiredPermissions) {
    return authorizePermission(requiredPermissions)(req, res, next);
  }

  // 其他路由需要基本认证
  return proceedAuth();
};

// 认证服务路由 - 包含公开路由 (login, register) 和需要认证的路由 (user)
app.use('/api/auth', 
  createServiceLimiter('auth'),
  createServiceProxy('auth')
);

app.use('/api/user',
  createServiceLimiter('auth'),
  authenticateToken,
  createServiceProxy('auth')
);

// NLP服务路由 - 需要认证和权限
app.use('/api/nlp',
  createServiceLimiter('nlp'),
  authenticateToken,
  checkPermissions,
  createServiceProxy('nlp')
);

// 金融服务路由 - 需要认证和权限
app.use('/api/financial',
  createServiceLimiter('financial'),
  authenticateToken,
  checkPermissions,
  createServiceProxy('financial')
);

// SSE端点 - 不需要认证（因为EventSource无法发送自定义头）
app.use('/api/equity/sse',
  createServiceLimiter('financial'),
  createServiceProxy('financial')
);

// 金融服务（股权实时）等别名路由，兼容 /api/equity 前缀
app.use('/api/equity',
  createServiceLimiter('financial'),
  authenticateToken,
  checkPermissions,
  createServiceProxy('financial')
);

// 文档服务路由 - 需要认证和权限
app.use('/api/document',
  createServiceLimiter('document'),
  authenticateToken,
  checkPermissions,
  createServiceProxy('document')
);

// API概览
app.get('/api', optionalAuth, (req, res) => {
  const userPermissions = req.user?.permissions || [];
  
  const availableEndpoints = {};
  
  // 根据用户权限显示可用端点
  if (userPermissions.includes('chatbot:use')) {
    availableEndpoints.nlp = {
      chat: '/api/nlp/chat',
      sentiment: userPermissions.includes('sentiment:analyze') ? '/api/nlp/sentiment' : null
    };
  }
  
  if (userPermissions.includes('equity:penetration') || userPermissions.includes('equity:pledge') || userPermissions.includes('bond:analyze')) {
    availableEndpoints.financial = {
      equity_penetration: userPermissions.includes('equity:penetration') ? '/api/financial/equity-penetration' : null,
      equity_pledge: userPermissions.includes('equity:pledge') ? '/api/financial/equity-pledge' : null,
      bond_analysis: userPermissions.includes('bond:analyze') ? '/api/financial/bond-analysis' : null
    };
  }
  
  if (userPermissions.includes('document:ocr') || userPermissions.includes('document:watermark') || userPermissions.includes('document:excel')) {
    availableEndpoints.document = {
      ocr: userPermissions.includes('document:ocr') ? '/api/document/ocr' : null,
      watermark: userPermissions.includes('document:watermark') ? '/api/document/watermark' : null,
      excel_merge: userPermissions.includes('document:excel') ? '/api/document/excel-merge' : null
    };
  }
  
  res.json({
    message: 'IDEALAB API Gateway',
    version: '1.0.0',
    user: req.user ? {
      id: req.user.userId,
      role: req.user.role,
      permissions: req.user.permissions
    } : null,
    endpoints: availableEndpoints,
    public_endpoints: {
      auth: {
        login: '/api/auth/login',
        register: '/api/auth/register',
        refresh: '/api/auth/refresh'
      },
      health: '/health'
    }
  });
});

// 错误处理
app.use((error, req, res, next) => {
  logger.error(`Gateway Error [${req.id}]:`, error);
  res.status(500).json({
    success: false,
    message: 'Internal Gateway Error',
    requestId: req.id,
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false,
    message: 'Route not found',
    requestId: req.id
  });
});

// 启动服务并处理 WebSocket 升级
const server = http.createServer(app);

server.on('upgrade', (req, socket, head) => {
  const url = req.url || '';
  logger.info(`WebSocket upgrade: ${url}`);

  if (url.startsWith('/api/equity/ws/')) {
    // 使用全局WebSocket代理实例
    wsProxy.upgrade(req, socket, head);
  } else {
    socket.destroy();
  }
});

server.listen(PORT, () => {
  logger.info(`🚀 API Gateway running on port ${PORT}`);
  logger.info(`📋 Health check available at http://localhost:${PORT}/health`);
  logger.info(`🔗 API overview available at http://localhost:${PORT}/api`);
});