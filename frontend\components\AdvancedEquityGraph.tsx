import React, { useEffect, useRef, useState } from 'react';
import { Button, Tooltip, Select, Space, AutoComplete, Spin } from 'antd';
import { ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';
import cytoscape from 'cytoscape';
import dagre from 'cytoscape-dagre';
import coseBilkent from 'cytoscape-cose-bilkent';
import fcose from 'cytoscape-fcose';
import avsdf from 'cytoscape-avsdf';
import { GraphLegend } from './GraphLegend';



cytoscape.use(dagre);
cytoscape.use(coseBilkent);
cytoscape.use(fcose);
cytoscape.use(avsdf);

// The EquityNode interface is now simplified as detailed properties are handled by the backend.
export interface EquityNode {
  id: string;
  name: string;
  type: 'company' | 'listed' | 'partnership' | 'person';
  node_size: number;
  properties: Record<string, any>;
  expandable: {
    up: boolean;
    down: boolean;
  };
}

interface AdvancedEquityGraphProps {
  data: {
    nodes: EquityNode[];
    edges: any[];
  };
  onNodeClick?: (node: EquityNode) => void;
  onNodeExpand?: (nodeId: string, direction: 'up' | 'down') => void;
  height?: number | string;
  searchProps?: {
    value: string;
    options: { value: string; label?: string }[];
    onSearch: (value: string) => void;
    onSelect: (value: string) => void;
    loading?: boolean;
    onKeyPress?: (e: React.KeyboardEvent) => void;
  };
  equityThreshold?: number;
  relationshipFilter?: {
    showShareholders: boolean;
    showInvestments: boolean;
  };
  selectedTags?: Set<string>;
  onVisibleNodesChange?: (visibleCount: number) => void;
}

// 数据清洗函数
const cleanStakeData = (input: any): number => {
  if (!input || input === '-') return -1; // 用-1表示数据缺失

  const str = input.toString().replace(/[%\s]/g, '');
  const num = parseFloat(str);

  if (isNaN(num) || num < 0) return -1;           // 无效数据
  if (num >= 1900 && num <= 2100) return -1;     // 年份数据
  if (num > 100) return num > 10000 ? -1 : num/100; // 可能未转换的百分比

  return num;
};

const LAYOUT_CONFIGS = {
  dagre: { name: 'dagre', rankDir: 'TB', spacingFactor: 1.2, nodeDimensionsIncludeLabels: true, animate: true, animationDuration: 500, fit: true, padding: 20 },
  fcose: {
    name: 'fcose',
    quality: 'proof',
    animate: true,
    animationDuration: 2000, // 增加动画时间让布局更稳定
    fit: true,
    padding: 20, // 减少边距，让图形更贴近边缘
    // 针对星型网络的特殊配置 - 大幅增强集团分离
    gravity: 0.3, // 极低重力，几乎不向中心聚拢
    gravityRangeCompound: 3.0,
    gravityCompound: 0.2,
    gravityRange: 8.0,
    initialEnergyOnIncremental: 0.8,

    // 强化分散效果
    nodeSeparation: 200, // 大幅增加最小节点分离距离
    piTol: 0.0000001,

    // 重新设计边长：最大化集团间距离
    idealEdgeLength: (edge: any) => {
      try {
        const sourceNode = edge.source();
        const targetNode = edge.target();
        if (!sourceNode || !targetNode) return 200;

        const sourceDegree = sourceNode.degree() || 0;
        const targetDegree = targetNode.degree() || 0;
        const maxDegree = Math.max(sourceDegree, targetDegree);
        const minDegree = Math.min(sourceDegree, targetDegree);

        let edgeLength = 200;

        // 高度数节点之间：超远距离，强制分离不同集团
        if (sourceDegree >= 5 && targetDegree >= 5) {
          edgeLength = 600 + Math.random() * 200; // 600-800px 超远距离
        }
        // 超高度数节点（中心）到其他节点：分层距离
        else if (maxDegree >= 15) {
          if (minDegree === 1) {
            edgeLength = 100 + Math.random() * 80; // 150-230px
          } else if (minDegree >= 2 && minDegree <= 4) {
            edgeLength = 250 + Math.random() * 60; // 250-310px
          } else {
            edgeLength = 400 + Math.random() * 100; // 400-500px，高度数节点远离中心
          }
        }
        // 中等度数节点之间：中等距离
        else if (sourceDegree >= 2 && targetDegree >= 2) {
          const avgDegree = (sourceDegree + targetDegree) / 2;
          edgeLength = 100 + (avgDegree * 30) + Math.random() * 50;
        }
        // 低度数节点之间：适中距离，避免聚集
        else if (sourceDegree === 1 && targetDegree === 1) {
          edgeLength = 10 + Math.random() * 60; // 50-110px
        }
        // 其他情况
        else {
          edgeLength = 80 + Math.random() * 60; // 150-210px
        }

        return edgeLength;
      } catch (e) {
        return 200;
      }
    },

    // 极大化节点斥力：强制集团分离
    nodeRepulsion: (node: any) => {
      try {
        if (!node) return 15000;
        const degree = node.degree() || 0;
        let repulsion = 15000;

        // 超高度数节点：极强斥力，成为独立的强中心
        if (degree >= 15) {
          repulsion = 100000 + (degree * 5000); // 100000-230000 极强斥力
        }
        // 高度数节点：超强斥力，形成独立集团中心
        else if (degree >= 5) {
          repulsion = 60000 + (degree * 4000); // 60000-116000 超强斥力
        }
        // 中度数节点：强斥力
        else if (degree >= 2) {
          repulsion = 18000 + (degree * 2000); // 25000-33000
        }
        // 低度数节点：适中斥力，防止重叠但允许聚集
        else {
          repulsion = 6000; // 适中斥力
        }

        return repulsion;
      } catch (e) {
        return 4000;
      }
    }
  },
  'cose-bilkent': {
    name: 'cose-bilkent',
    animate: 'end',
    animationDuration: 800,
    gravity: 0.1,
    fit: true,
    padding: 20,
    nodeRepulsion: 15000,
    idealEdgeLength: 150,
  },
  concentric: {
    name: 'concentric',
    concentric: (node: any) => {
      // 专门为星型网络设计的同心圆布局
      const degree = node.data('degree') || node.degree() || 0;

      // 超高度数节点放在最中心
      if (degree >= 15) return 100;
      // 高度数节点在第二层
      else if (degree >= 5) return 80;
      // 中度数节点在第三层
      else if (degree >= 2) return 60;
      // 低度数节点在最外层，但分散开
      else return 40 - Math.random() * 20; // 20-40，加入随机性避免重叠
    },
    levelWidth: (node: any) => {
      // 根据节点度数调整层宽
      const degree = node.data('degree') || node.degree() || 0;
      if (degree >= 15) return 1; // 中心节点紧凑
      else if (degree >= 5) return 2; // 高度数节点稍宽
      else if (degree >= 2) return 3; // 中度数节点更宽
      else return 4; // 低度数节点最宽，分散排列
    },
    minNodeSpacing: 150, // 增加节点间距
    animate: true,
    animationDuration: 800,
    fit: true,
    padding: 20
  },
  avsdf: { name: 'avsdf', nodeSeparation: 120, fit: true, padding: 20 }
};

const graphStylesheet: any[] = [
  {
    selector: 'node',
    style: {
      'font-family': 'sans-serif',
      'label': 'data(name)',
      'color': '#e2e8f0',
      'font-size': 14,
      'text-valign': 'bottom',
      'text-halign': 'center',
      'text-margin-y': 8,
      'shape': 'ellipse', // Default shape to circle
      'width': (ele: any) => {
        const size = ele.data('node_size') || 10000;
        const scaledSize = Math.log10(Math.max(1, size)) * 15;
        return Math.max(40, Math.min(120, scaledSize));
      },
      'height': (ele: any) => {
        const size = ele.data('node_size') || 10000;
        const scaledSize = Math.log10(Math.max(1, size)) * 15;
        return Math.max(40, Math.min(120, scaledSize));
      },
      'transition-property': 'background-color, border-color, width, height',
      'transition-duration': '0.3s',
      'text-background-color': 'rgba(15, 23, 42, 0.7)',
      'text-background-opacity': 1,
      'text-background-padding': '4px',
      'text-background-shape': 'round-rectangle',
      'border-width': 2,
      'border-color': '#475569'
    },
  },
  // Type-specific styles
  { selector: 'node[type="company"]', style: { 'background-color': '#3b82f6', 'border-color': '#1d4ed8' } },
  { selector: 'node[type="listed"]', style: { 'background-color': '#f59e0b', 'border-color': '#b45309' } },
  { selector: 'node[type="partnership"]', style: { 'background-color': '#10b981', 'border-color': '#047857' } },
  { selector: 'node[type="person"]', style: { 'background-color': '#8b5cf6', 'border-color': '#6d28d9' } },
  {
    selector: 'edge',
    style: {
      'width': (ele: any) => {
        const percentage = cleanStakeData(ele.data('properties')?.percentage);
        if (percentage === -1) return 2; // 问题数据用固定宽度
        return Math.max(1, (percentage / 100) * 8);
      },
      'line-color': (ele: any) => {
        const percentage = cleanStakeData(ele.data('properties')?.percentage);
        return percentage === -1 ? '#ef4444' : '#64748b'; // 问题数据用红色
      },
      'line-style': (ele: any) => {
        const percentage = cleanStakeData(ele.data('properties')?.percentage);
        return percentage === -1 ? 'dashed' : 'solid'; // 问题数据用虚线
      },
      'target-arrow-shape': 'triangle',
      'target-arrow-color': (ele: any) => {
        const percentage = cleanStakeData(ele.data('properties')?.percentage);
        return percentage === -1 ? '#ef4444' : '#64748b';
      },
      'curve-style': 'unbundled-bezier',
      'control-point-distances': '20 -20',
      'control-point-weights': '0.25 0.75',
      'transition-property': 'line-color, target-arrow-color, width',
      'transition-duration': '0.3s',
      'opacity': 0.7,
    },
  },
  { selector: 'edge[type="control"]', style: { 'line-color': '#f43f5e', 'target-arrow-color': '#f43f5e', 'line-style': 'solid' } },
  { selector: 'edge[type="holding"]', style: { 'line-color': '#eab308', 'target-arrow-color': '#eab308', 'line-style': 'dashed' } },
  { selector: 'edge[type="partnership"]', style: { 'line-color': '#0ea5e9', 'target-arrow-color': '#0ea5e9', 'line-style': 'dotted' } },

  // Interaction styles
  { selector: 'node:selected', style: { 'border-width': 5, 'border-color': '#f43f5e', 'z-index': 99 } },
  { selector: '.highlighted', style: { 'border-color': '#38bdf8', 'border-width': 4, 'z-index': 99 } },
  { selector: '.faded', style: { 'opacity': 0.15, 'transition-duration': '0.2s' } },

  // 隐藏被阈值过滤的元素
  { selector: '.threshold-hidden', style: { 'display': 'none' } },
  // 隐藏被关系过滤的元素
  { selector: '.relationship-hidden', style: { 'display': 'none' } },
  // 标签高亮样式
  { selector: '.tag-highlighted', style: {
    'border-width': 6,
    'border-color': '#faad14',
    'border-style': 'solid',
    'z-index': 99
  } },
];

const AdvancedEquityGraph: React.FC<AdvancedEquityGraphProps> = ({ data, onNodeClick, onNodeExpand, height = "100%", searchProps, equityThreshold = 0, relationshipFilter = { showShareholders: true, showInvestments: true }, selectedTags = new Set(), onVisibleNodesChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cyRef = useRef<cytoscape.Core | null>(null);
  const [currentLayout, setCurrentLayout] = useState<string>('fcose'); // 设为默认布局
  const [tooltip, setTooltip] = useState<{ content: string; x: number; y: number; visible: boolean } | null>(null);

  // 获取当前搜索的公司名称用于过滤逻辑
  const companyName = searchProps?.value || '';

  useEffect(() => {
    if (!containerRef.current || !data.nodes.length) return;

    try {
      // 验证数据完整性 - 不再在这里过滤数据
      const validNodes = data.nodes.filter(n => n.id && n.name);
      const validEdges = data.edges.filter(e => e.source && e.target);

      if (validNodes.length === 0) {
        return;
      }

      // 先规范化 ID 为字符串，避免数值 ID 与 Cytoscape 匹配失败
      const normalizedNodes = validNodes.map((n) => ({ ...n, id: String(n.id) }));
      const normalizedEdges = validEdges.map((e) => ({
        source: String(e.source),
        target: String(e.target),
        type: e.type,
        properties: e.properties,
      }));

      // 计算节点度数，用于布局算法
      const degreeMap = new Map<string, number>();
      normalizedNodes.forEach((node) => {
        const degree = normalizedEdges.filter(
          (e) => e.source === node.id || e.target === node.id
        ).length;
        degreeMap.set(node.id, degree);
      });

      // 组装 Cytoscape 元素
      const nodes = normalizedNodes.map((n) => ({
        group: 'nodes' as const,
        data: {
          ...n,
          ...n.properties,
          degree: degreeMap.get(n.id) || 0,
        },
      }));

      const edges = normalizedEdges.map((e, idx) => ({
        group: 'edges' as const,
        data: {
          id: `${e.source}->${e.target}-${idx}`,
          source: e.source,
          target: e.target,
          type: e.type,
          properties: e.properties,
        },
      }));

      const elements = [...nodes, ...edges];

      // 安全地获取布局配置
      const layoutConfig = LAYOUT_CONFIGS[currentLayout as keyof typeof LAYOUT_CONFIGS] || LAYOUT_CONFIGS.dagre;

      cyRef.current = cytoscape({
        container: containerRef.current,
        elements: elements,
        style: graphStylesheet,
        layout: layoutConfig,
        // wheelSensitivity: 0.5, // 移除自定义滚轮敏感度以消除警告
      });

      const cy = cyRef.current;

      cy.on('tap', 'node', (event) => {
        const node = event.target;
        const nodeData = node.data();

        cy.elements().removeClass('faded');
        cy.getElementById(nodeData.id).neighborhood().addClass('highlighted');
        if (onNodeClick) { onNodeClick(nodeData); }
      });

      cy.on('mouseover', 'node', (event) => {
        const node = event.target;
        const data = node.data();
        const pos = event.renderedPosition;

          document.body.style.cursor = 'pointer';

        const neighborhood = node.neighborhood();
        cy.elements().addClass('faded');
        node.removeClass('faded').addClass('highlighted');
        neighborhood.removeClass('faded');

        const content = `
          <div style="font-weight: bold; margin-bottom: 4px; color: #f1f5f9;">${data.name}</div>
          <div style="color: #cbd5e1;">法定代表人: ${data.法定代表人 || 'N/A'}</div>
          <div style="color: #cbd5e1;">注册资本: ${data.注册资本 || 'N/A'}</div>
        `;
        setTooltip({ content, x: pos.x, y: pos.y, visible: true });
      });

      cy.on('mouseover', 'edge', (event) => {
        const edge = event.target;
        const data = edge.data();
        const pos = event.renderedPosition;

        const originalValue = data.properties?.percentage;
        const cleanedValue = cleanStakeData(originalValue);

        const content = `
          <div style="font-weight: bold; margin-bottom: 4px; color: #f1f5f9;">股权关系</div>
          <div style="color: #cbd5e1;">持股比例: ${cleanedValue === -1 ? '数据异常' : cleanedValue.toFixed(2) + '%'}</div>
          ${cleanedValue === -1 ? `<div style="color: #fbbf24; font-size: 11px; margin-top: 2px;">原值: "${originalValue}"</div>` : ''}
        `;
        setTooltip({ content, x: pos.x, y: pos.y, visible: true });
      });

      cy.on('mouseout', 'node, edge', () => {
        cy.elements().removeClass('faded highlighted');
        document.body.style.cursor = 'default';
        setTooltip(prev => prev ? { ...prev, visible: false } : null);
      });

      cy.on('pan zoom', () => {
        setTooltip(null);
      });

      return () => {
        try {
          cy?.destroy();
        } catch (e) {
          // Silently handle destroy errors
        }
      };
    } catch (error) {
      console.error('Error initializing cytoscape:', error);
    }
  }, [data, onNodeClick, onNodeExpand, currentLayout]);

  // 单独的useEffect处理股权阈值和关系过滤，避免重新布局
  useEffect(() => {
    const cy = cyRef.current;
    if (!cy || !cy.elements().length) return;

    try {
      // 重置所有元素为可见
      cy.elements().removeClass('threshold-hidden relationship-hidden');

      // 首先处理关系类型过滤
      if (!relationshipFilter.showShareholders || !relationshipFilter.showInvestments) {
        // 智能查找中心节点
        let searchedNodes = cy.nodes().filter(node => {
          const nodeName = node.data('name') || node.data('企业名称');
          return nodeName === companyName;
        });

        // 如果精确匹配失败，尝试部分匹配
        if (searchedNodes.length === 0 && companyName) {
          searchedNodes = cy.nodes().filter(node => {
            const nodeName = node.data('name') || node.data('企业名称');
            return nodeName && (nodeName.includes(companyName) || companyName.includes(nodeName));
          });
        }

        // 如果还是找不到，选择连接度最高的节点作为中心节点
        if (searchedNodes.length === 0) {
          let maxDegree = 0;
          let centerNode = null;
          cy.nodes().forEach(node => {
            const degree = node.degree();
            if (degree > maxDegree) {
              maxDegree = degree;
              centerNode = node;
            }
          });
          if (centerNode) {
            searchedNodes = cy.collection([centerNode]);
          }
        }

        if (searchedNodes.length > 0) {
          const centerNode = searchedNodes[0];
          const centerNodeId = centerNode.id();

          // 第一步：根据关系类型隐藏边
          let hasHiddenEdges = false;
          cy.edges().forEach(edge => {
            const sourceId = edge.source().id();
            const targetId = edge.target().id();

            let shouldHide = false;

            // 判断是否是股东关系（其他节点指向中心节点）
            if (!relationshipFilter.showShareholders && targetId === centerNodeId) {
              shouldHide = true;
            }

            // 判断是否是对外投资（中心节点指向其他节点）
            if (!relationshipFilter.showInvestments && sourceId === centerNodeId) {
              shouldHide = true;
            }

            if (shouldHide) {
              edge.addClass('relationship-hidden');
              hasHiddenEdges = true;
            }
          });

          // 第二步：如果有边被隐藏，递归隐藏所有不可达的节点和边
          if (hasHiddenEdges) {
            // 使用BFS从中心节点开始，找到所有可达的节点
            const visitedNodes = new Set([centerNodeId]);
            const queue = [centerNode];

            while (queue.length > 0) {
              const currentNode = queue.shift();
              if (!currentNode) continue;

              // 遍历当前节点的所有可见边
              currentNode.connectedEdges().forEach(edge => {
                if (!edge.hasClass('relationship-hidden')) {
                  const source = edge.source();
                  const target = edge.target();
                  const otherNode = source.id() === currentNode.id() ? target : source;
                  const otherNodeId = otherNode.id();

                  if (!visitedNodes.has(otherNodeId)) {
                    visitedNodes.add(otherNodeId);
                    queue.push(otherNode as any);
                  }
                }
              });
            }

            // 隐藏所有不可达的节点和边
            cy.nodes().forEach(node => {
              if (!visitedNodes.has(node.id()) && !node.hasClass('relationship-hidden')) {
                node.addClass('relationship-hidden');
              }
            });

            cy.edges().forEach(edge => {
              if (!edge.hasClass('relationship-hidden')) {
                const sourceId = edge.source().id();
                const targetId = edge.target().id();

                if (!visitedNodes.has(sourceId) || !visitedNodes.has(targetId)) {
                  edge.addClass('relationship-hidden');
                }
              }
            });
          }
        }
      }

      // 然后处理股权阈值过滤
      if (equityThreshold > 0) {
        cy.edges().forEach(edge => {
          if (!edge.hasClass('relationship-hidden')) {
            const percentage = cleanStakeData(edge.data('properties')?.percentage);
            if (percentage !== -1 && percentage < equityThreshold) {
              edge.addClass('threshold-hidden');
            }
          }
        });
      }

      // 最后隐藏孤立节点
      cy.nodes().forEach(node => {
        const connectedVisibleEdges = node.connectedEdges().filter(edge =>
          !edge.hasClass('threshold-hidden') && !edge.hasClass('relationship-hidden')
        );
        if (connectedVisibleEdges.length === 0) {
          const isSearchedCompany = node.data('name') === companyName ||
                                 node.data('企业名称') === companyName;
          if (!isSearchedCompany) {
            node.addClass('threshold-hidden');
          }
        }
      });

      // 通知父组件可见节点数量
      const visibleNodes = cy.nodes().filter(node =>
        !node.hasClass('threshold-hidden') && !node.hasClass('relationship-hidden')
      );
      if (onVisibleNodesChange) {
        onVisibleNodesChange(visibleNodes.length);
      }
    } catch (error) {
      console.error('关系过滤错误:', error);
    }
  }, [equityThreshold, relationshipFilter, companyName]);

  // 单独的useEffect处理标签高亮
  useEffect(() => {
    const cy = cyRef.current;
    if (!cy || !cy.elements().length) return;

    try {
      // 重置所有节点的标签高亮状态
      cy.nodes().removeClass('tag-highlighted');

      if (selectedTags.size > 0) {
        cy.nodes().forEach(node => {
          const tags = node.data('标签') || node.data('properties')?.标签;
          if (tags && typeof tags === 'string') {
            const tagList = tags.split('|').map(tag => tag.trim()).filter(tag => tag);
            const hasSelectedTag = tagList.some(tag => selectedTags.has(tag));
            if (hasSelectedTag) {
              node.addClass('tag-highlighted');
            }
          }
        });
      }
    } catch (error) {
      console.error('标签高亮错误:', error);
    }
  }, [selectedTags]);

  useEffect(() => {
    if (cyRef.current) {
      try {
        const layoutConfig = LAYOUT_CONFIGS[currentLayout as keyof typeof LAYOUT_CONFIGS] || LAYOUT_CONFIGS.dagre;
        const layout = cyRef.current.layout(layoutConfig);
        layout.run();
      } catch (error) {
        console.error('Error switching layout:', error);
        // 回退到默认布局
        try {
          const defaultLayout = cyRef.current.layout(LAYOUT_CONFIGS.dagre);
          defaultLayout.run();
        } catch (fallbackError) {
          console.error('Fallback layout also failed:', fallbackError);
        }
      }
    }
  }, [currentLayout]);

  const handleLayoutChange = (layout: string) => { setCurrentLayout(layout); };

  const handleControlClick = (action: 'zoomIn' | 'zoomOut' | 'fit') => {
    const cy = cyRef.current;
    if (!cy) return;
    if (action === 'zoomIn') cy.zoom({ level: cy.zoom() * 1.2, renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 } });
    if (action === 'zoomOut') cy.zoom({ level: cy.zoom() / 1.2, renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 } });
    if (action === 'fit') cy.fit(undefined, 50);
  };

  return (
    <div style={{ position: 'relative', width: '100%', height, minHeight: 480, overflow: 'hidden', margin: 0, padding: 0, display: 'flex', flexDirection: 'column' }}>
      <div ref={containerRef} style={{ width: '100%', height: '100%', background: 'transparent', margin: 0, padding: 0, flex: 1 }} />

      {tooltip && (
        <div
            dangerouslySetInnerHTML={{ __html: tooltip.content }}
            style={{
                position: 'absolute',
                left: tooltip.x + 20,
                top: tooltip.y - 10,
                background: 'rgba(15, 23, 42, 0.85)',
                color: 'white',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                backdropFilter: 'blur(4px)',
                zIndex: 100,
                pointerEvents: 'none',
                fontSize: '13px',
                maxWidth: '300px',
                transition: 'opacity 0.2s, transform 0.2s',
                opacity: tooltip.visible ? 1 : 0,
                transform: tooltip.visible ? 'translateY(0)' : 'translateY(10px)',
            }}
        />
      )}

      <div style={{ position: 'absolute', top: 16, left: 16, right: 16, zIndex: 10 }}>
        {/* 第一行：搜索控件 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px', flexWrap: 'wrap' }}>
          <AutoComplete
            style={{ width: 'min(280px, calc(100% - 100px))', minWidth: '200px' }}
            options={searchProps?.options || []}
            onSelect={searchProps?.onSelect || (() => {})}
            onSearch={searchProps?.onSearch || (() => {})}
            placeholder="输入公司名进行查询（支持中文模糊搜索）"
            value={searchProps?.value || ''}
            disabled={!searchProps}
            onKeyDown={searchProps?.onKeyPress}
            allowClear
            showSearch
            filterOption={false} // 禁用本地过滤，使用服务器端搜索
            notFoundContent={searchProps?.loading ? <Spin size="small" /> : '暂无匹配结果'}
          />
          <Button
            type="primary"
            onClick={() => searchProps?.onSelect?.(searchProps.value)}
            loading={searchProps?.loading}
            style={{ flexShrink: 0 }}
            disabled={!searchProps || !searchProps.value}
          >
            查询
          </Button>
        </div>

        {/* 第二行：控制按钮 */}
        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
          <Space size="small" wrap>
            <Tooltip title="放大"><Button ghost size="small" icon={<ZoomIn size={14} />} onClick={() => handleControlClick('zoomIn')} /></Tooltip>
            <Tooltip title="缩小"><Button ghost size="small" icon={<ZoomOut size={14} />} onClick={() => handleControlClick('zoomOut')} /></Tooltip>
            <Tooltip title="适应屏幕"><Button ghost size="small" icon={<Maximize2 size={14} />} onClick={() => handleControlClick('fit')} /></Tooltip>
            <Select
              value={currentLayout}
              onChange={handleLayoutChange}
              size="small"
              style={{ width: 'min(140px, calc(100vw - 200px))', minWidth: '120px' }}
              options={[
                { value: 'fcose', label: '智能防重叠' },
                { value: 'concentric', label: '星型同心圆' },
                { value: 'dagre', label: '层级树状' },
                { value: 'cose-bilkent', label: '经典力导向' },
                { value: 'avsdf', label: '对称布局' },
              ]}
            />
          </Space>
        </div>
      </div>

      <div style={{ position: 'absolute', bottom: 0, left: 8, zIndex: 10 }}>
        <GraphLegend />
      </div>
    </div>
  );
};

export default AdvancedEquityGraph;
