#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权树抓取调度器 - 异步版本
集成到IDEALAB项目，支持进度回调和数据库存储
"""
import asyncio
import time
import random
from collections import deque
from urllib.parse import urljoin
import re
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging

from .tianyancha_scraper import TianyanchaScraperAsync
from .progress_logger import ProgressLogger
from ..graph.neo4j_client import Neo4jClient

class TreeCrawlerAsync:
    """异步股权树抓取调度器"""
    
    def __init__(self, neo4j_client: Neo4jClient, progress_logger: Optional[ProgressLogger] = None):
        self.neo4j_client = neo4j_client
        self.progress_logger = progress_logger
        self.scraper = None
        self.logger = logging.getLogger("tree_crawler_async")
    
    async def crawl_equity_tree(self, company_name: str, depth: int = 2, 
                               direction: str = "both") -> Dict[str, Any]:
        """
        异步爬取股权树
        
        Args:
            company_name: 公司名称
            depth: 穿透深度
            direction: 方向 ('up', 'down', 'both')
        
        Returns:
            包含节点和边的图数据
        """
        try:
            # 初始化爬虫（参考run_crawler.py的设置）
            self.scraper = TianyanchaScraperAsync(
                use_selenium=True,  # 强制使用Selenium模式
                headless=False,     # 不使用无头模式，便于调试和连接现有浏览器
                progress_logger=self.progress_logger
            )
            
            if self.progress_logger:
                self.progress_logger.set_current_phase("搜索目标公司")
            
            # 第一步：搜索公司
            search_result = await self.scraper.search_company(company_name)
            if not search_result:
                raise Exception(f"未找到公司: {company_name}")
            
            # 初始化BFS队列
            queue = deque([(search_result['url'], 0, search_result['name'])])
            visited_urls = {search_result['url']}
            all_nodes = []
            all_edges = []
            
            if self.progress_logger:
                self.progress_logger.set_current_phase("执行股权关系分析")
                # 估算总步数
                estimated_steps = self._estimate_total_nodes(depth, direction)
                self.progress_logger.set_total_steps(estimated_steps)
            
            # 执行BFS爬取
            while queue:
                current_url, current_depth, company_name_current = queue.popleft()
                
                if current_depth >= depth:
                    continue
                
                if self.progress_logger:
                    self.progress_logger.log_company_start(company_name_current, current_depth)
                
                try:
                    # 检查数据库缓存
                    cached_data = await self._check_database_cache(current_url)
                    
                    if cached_data:
                        company_data = cached_data
                        if self.progress_logger:
                            self.progress_logger._send_user_log("info", f"使用缓存数据: {company_name_current}")
                    else:
                        # 爬取新数据
                        company_data = await self.scraper.scrape_company_info(current_url, direction)
                        if company_data:
                            # 存储到数据库
                            await self._save_to_database(company_data)
                    
                    if not company_data:
                        if self.progress_logger:
                            self.progress_logger.log_company_skip(company_name_current, "无法获取数据")
                        continue
                    
                    # 处理节点数据
                    node_data = self._extract_node_data(company_data)
                    all_nodes.append(node_data)
                    
                    # 处理关系数据
                    edges = self._extract_relationship_data(company_data, current_depth)
                    all_edges.extend(edges)
                    
                    # 添加新的URL到队列
                    new_urls = self._extract_new_urls(company_data, direction)
                    new_count = 0
                    for url, name in new_urls:
                        if url not in visited_urls and current_depth + 1 < depth:
                            visited_urls.add(url)
                            queue.append((url, current_depth + 1, name))
                            new_count += 1
                    
                    if self.progress_logger:
                        shareholders_count = len(company_data.get('shareholders_info', []))
                        investments_count = len(company_data.get('investments_info', []))
                        self.progress_logger.log_company_complete(
                            company_name_current, shareholders_count, investments_count
                        )
                
                except Exception as e:
                    if self.progress_logger:
                        self.progress_logger.log_error(company_name_current, str(e))
                    self.logger.error(f"Failed to crawl {company_name_current}: {e}")
                
                # 添加延迟，避免过快请求
                delay = random.uniform(2, 4)
                await asyncio.sleep(delay)
            
            if self.progress_logger:
                self.progress_logger.set_current_phase("数据整理与存储")
            
            # 数据后处理
            result = await self._post_process_data(all_nodes, all_edges)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Crawl failed for {company_name}: {e}")
            raise
        
        finally:
            if self.scraper:
                self.scraper.close()
    
    async def _check_database_cache(self, url: str) -> Optional[Dict[str, Any]]:
        """检查数据库缓存"""
        try:
            # 从URL提取公司ID
            t_id_match = re.search(r'company/(\d+)', url)
            if not t_id_match:
                return None
            
            company_id = int(t_id_match.group(1))
            
            # 查询Neo4j数据库
            # 这里应该调用实际的Neo4j查询方法
            # cached_data = await self.neo4j_client.get_company_by_tianyancha_id(company_id)
            
            # 暂时返回None，表示没有缓存
            return None
            
        except Exception as e:
            self.logger.error(f"Cache check failed for {url}: {e}")
            return None
    
    async def _save_to_database(self, company_data: Dict[str, Any]):
        """保存数据到数据库"""
        try:
            # 这里应该调用实际的数据库保存方法
            # await self.neo4j_client.save_company_and_relationships(company_data)
            pass
            
        except Exception as e:
            self.logger.error(f"Database save failed: {e}")
    
    def _extract_node_data(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取节点数据"""
        basic_info = company_data.get('basic_info', {})
        company_name = basic_info.get('企业名称', '未知公司')
        
        return {
            'id': str(hash(company_name)),
            'name': company_name,
            'label': company_name,
            'type': 'company',
            'properties': {
                'legal_representative': basic_info.get('法定代表人'),
                'registered_capital': basic_info.get('注册资本'),
                'status': basic_info.get('经营状态'),
                'establishment_date': basic_info.get('成立日期'),
                'credit_code': basic_info.get('统一社会信用代码'),
                'tags': basic_info.get('标签', ''),
                'source_url': company_data.get('source_url', '')
            }
        }
    
    def _extract_relationship_data(self, company_data: Dict[str, Any], depth: int) -> List[Dict[str, Any]]:
        """提取关系数据"""
        edges = []
        basic_info = company_data.get('basic_info', {})
        company_name = basic_info.get('企业名称', '未知公司')
        company_id = str(hash(company_name))
        
        # 处理股东关系
        for shareholder in company_data.get('shareholders_info', []):
            shareholder_name = shareholder.get('股东名称')
            if shareholder_name:
                edge = {
                    'source': str(hash(shareholder_name)),
                    'target': company_id,
                    'type': 'HOLDS_SHARE',
                    'properties': {
                        'percentage': shareholder.get('持股比例', ''),
                        'investment_amount': shareholder.get('认缴出资额万元', ''),
                        'depth': depth,
                        'relationship_type': 'shareholding'
                    }
                }
                edges.append(edge)
        
        # 处理投资关系
        for investment in company_data.get('investments_info', []):
            investment_name = investment.get('被投公司名称')
            if investment_name:
                edge = {
                    'source': company_id,
                    'target': str(hash(investment_name)),
                    'type': 'HOLDS_SHARE',
                    'properties': {
                        'percentage': investment.get('投资占比', ''),
                        'status': investment.get('状态', ''),
                        'registered_capital': investment.get('注册资本', ''),
                        'depth': depth + 1,
                        'relationship_type': 'investment'
                    }
                }
                edges.append(edge)
        
        return edges
    
    def _extract_new_urls(self, company_data: Dict[str, Any], direction: str) -> List[tuple]:
        """提取新的URL用于继续爬取"""
        urls = []
        
        if direction in ['up', 'both']:
            for shareholder in company_data.get('shareholders_info', []):
                name = shareholder.get('股东名称')
                url = shareholder.get('股东链接')
                if name and url and 'company' in url:
                    full_url = urljoin('https://www.tianyancha.com', url)
                    urls.append((full_url, name))
        
        if direction in ['down', 'both']:
            for investment in company_data.get('investments_info', []):
                name = investment.get('被投公司名称')
                url = investment.get('被投公司链接')
                if name and url and 'company' in url:
                    full_url = urljoin('https://www.tianyancha.com', url)
                    urls.append((full_url, name))
        
        return urls
    
    async def _post_process_data(self, nodes_data: List, edges_data: List) -> Dict[str, Any]:
        """数据后处理"""
        if self.progress_logger:
            self.progress_logger._send_user_log("info", "正在整理数据结构...")
        
        # 去重和清理节点
        unique_nodes = {}
        for node in nodes_data:
            node_id = node['id']
            if node_id not in unique_nodes:
                unique_nodes[node_id] = node
            else:
                # 合并节点属性
                existing_node = unique_nodes[node_id]
                for key, value in node['properties'].items():
                    if value and not existing_node['properties'].get(key):
                        existing_node['properties'][key] = value
        
        nodes = list(unique_nodes.values())
        
        # 过滤有效边
        valid_node_ids = set(unique_nodes.keys())
        valid_edges = []
        
        for edge in edges_data:
            if edge['source'] in valid_node_ids and edge['target'] in valid_node_ids:
                valid_edges.append(edge)
        
        if self.progress_logger:
            self.progress_logger._send_user_log(
                "success", 
                f"✅ 数据整理完成: {len(nodes)} 个节点, {len(valid_edges)} 个关系"
            )
        
        return {
            'nodes': nodes,
            'edges': valid_edges,
            'metadata': {
                'total_nodes': len(nodes),
                'total_edges': len(valid_edges),
                'crawl_time': datetime.now().isoformat(),
                'data_source': 'tianyancha'
            }
        }
    
    def _estimate_total_nodes(self, depth: int, direction: str) -> int:
        """估算总节点数"""
        base_nodes = 1  # 根节点
        
        if direction == 'both':
            multiplier = 3  # 上下都爬取
        elif direction in ['up', 'down']:
            multiplier = 2  # 单方向
        else:
            multiplier = 1
        
        # 简单的指数增长估算
        for i in range(1, depth + 1):
            base_nodes += (multiplier ** i)
        
        return min(base_nodes, 100)  # 最多100个节点