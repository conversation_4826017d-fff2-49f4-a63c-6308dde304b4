import React from 'react';
import { Card, Typography, Space, Divider } from 'antd';

const { Text } = Typography;

const LEGEND_DATA = [
    { type: 'company', label: '公司/企业', color: '#3b82f6' },
    { type: 'partnership', label: '有限合伙', color: '#10b981' },
    { type: 'person', label: '个人', color: '#8b5cf6' },
];

const DATA_QUALITY_LEGEND = [
    { color: '#64748b', style: 'solid', label: '有效数据' },
    { color: '#ef4444', style: 'dashed', label: '数据异常' }
];

const ShapePreview: React.FC<{ color: string; }> = ({ color }) => {
    const style: React.CSSProperties = {
        width: '12px',
        height: '12px',
        backgroundColor: color,
        border: '1px solid rgba(255, 255, 255, 0.6)',
        marginRight: '8px',
        flexShrink: 0,
        borderRadius: '50%', // All nodes are circles now
    };

    return <div style={style} />;
};

const LinePreview: React.FC<{ color: string; style: string; }> = ({ color, style }) => {
    const lineStyle: React.CSSProperties = {
        width: '20px',
        height: '2px',
        backgroundColor: color,
        marginRight: '8px',
        flexShrink: 0,
        borderStyle: style === 'dashed' ? 'dashed' : 'solid',
        borderWidth: style === 'dashed' ? '1px 0' : '0',
        borderColor: color,
        background: style === 'dashed' ? 'transparent' : color,
    };

    return <div style={lineStyle} />;
};

export const GraphLegend: React.FC = () => (
    <Card
        size="small"
        bordered={false}
        style={{
            background: 'rgba(30, 41, 59, 0.7)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.15)',
            borderRadius: '8px 8px 0 0',
            margin: 0,
        }}
        bodyStyle={{ padding: '8px', margin: 0 }}
    >
        <Space direction="vertical" size={4}>
            <Text strong style={{ color: '#f1f5f9', fontSize: 12, marginBottom: '2px' }}>图例</Text>

            {/* 节点类型 */}
            {LEGEND_DATA.map(item => (
                <div key={item.type} style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
                    <ShapePreview color={item.color} />
                    <Text style={{ fontSize: 11, color: '#cbd5e1' }}>{item.label}</Text>
                </div>
            ))}

            <Divider style={{ margin: '4px 0 2px 0', borderColor: 'rgba(255, 255, 255, 0.2)' }} />

            {/* 数据质量 */}
            <Text strong style={{ color: '#f1f5f9', fontSize: 11, marginBottom: '2px' }}>数据质量</Text>
            {DATA_QUALITY_LEGEND.map((item, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
                    <LinePreview color={item.color} style={item.style} />
                    <Text style={{ fontSize: 11, color: '#cbd5e1' }}>{item.label}</Text>
                </div>
            ))}
        </Space>
    </Card>
);