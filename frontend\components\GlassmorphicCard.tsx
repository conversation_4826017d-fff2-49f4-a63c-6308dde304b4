import React from 'react';
import { Card, CardProps } from 'antd';

export const GlassmorphicCard: React.FC<CardProps> = ({ children, className, ...props }) => (
    <Card
        {...props}
        className={`glassmorphic-card ${className}`}
        style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            height: '100%',
            ...props.style,
        }}
    >
        {children}
    </Card>
); 